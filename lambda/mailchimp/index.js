const mailchimp = require("@mailchimp/mailchimp_marketing");
const crypto = require("crypto");
const {
  REGISTRATION_OPTIONS,
  FORM_TYPES,
  MARKET_OPTIONS,
  EMPLOYMENT_INTEREST_OPTIONS,
  CERTIFICATIONS_OPTIONS
} = require("../constants");
mailchimp.setConfig({
  apiKey: process.env.MAILCHIMP_API_KEY,
  server: process.env.MAILCHIMP_API_SERVER,
});

// TAGS
const FORM_REGISTRATION = "form/registration";
const FORM_CONTACT_US = "form/contactUs";
const JFK_FORM_CONTACT_US = "form/jfkcontactUs";

const FORM_BUSINESS_OPPORTUNITY = "form/businessOpportunities";
const FORM_EMPLOYMENT_OPPORTUNITY = "form/employmentOpportunities";
const FORM_STAY_UPTO_DATE = "form/stayUpToDate";
const SUPPLIER_DIVERSITY = "Supplier Diversity";

const SMS_REGISTRATION = "sms/registration";
const SMS_CONTACT_US = "sms/contactUs";
const SITE_URW_AIRPORTS = "site/urwairports";
const NTO = "NTO";

const addMember = async (formData, formType) => {

  const company = formData.company || formData.companyName;
  const homeAddress1 = formData.homeAddress1 || "";
  const homeAddress2 = formData.homeAddress2 || "";
  const businessAddress1 = formData.businessAddress1 || "";
  const businessAddress2 = formData.businessAddress2 || "";
  const airportExperience = formData.airportExperience || formData.airportEmploymentExperience || "";

  const tagsArr = [];
  let tags = Object.entries(formData)
    .filter(
      (item) => (item[0].includes("bi/") || item[0].includes("ei/") || item[0].includes("cert/") || item[0].includes("mi/")) && item[1]
    )
    .map((item) => item[0]);
  tags = [...REGISTRATION_OPTIONS, ...MARKET_OPTIONS, ...CERTIFICATIONS_OPTIONS, ...EMPLOYMENT_INTEREST_OPTIONS].forEach((item) =>
    tags.includes(item.key) ? tagsArr.push(item.value) : null
  );

  tagsArr.push(SITE_URW_AIRPORTS);

  switch (formType) {
    case FORM_TYPES.CONTACT_US:
      tagsArr.push(FORM_CONTACT_US);
      tagsArr.push(SMS_CONTACT_US);
      break;
    case FORM_TYPES.STAY_UP_TO_DATE:
      tagsArr.push(FORM_STAY_UPTO_DATE);
      break;
    case FORM_TYPES.REGISTER_NOW:
      tagsArr.push(FORM_REGISTRATION);
      tagsArr.push(SMS_REGISTRATION);
      tagsArr.push(SUPPLIER_DIVERSITY);
      break;
    case FORM_TYPES.BUSINESS_OPPORTUNITY:
      Object.keys(formData).forEach((key) => {
        if (
          key.includes("bi/") ||
          key.includes("cert/") ||
          key.includes("advancedNetworkNews") ||
          key.includes("emailUpdates")
        ) {
          if (formData[key] === true) {
            tagsArr.push(key);
          }
        }
      });
      tagsArr.push(FORM_BUSINESS_OPPORTUNITY);
      tagsArr.push(NTO);
      break;
    case FORM_TYPES.EMPLOYMENT_OPPORTUNITY:
      Object.keys(formData).forEach((key) => {
        if (
          key.includes("ei/") ||
          key.includes("advancedNetworkNews") ||
          key.includes("emailUpdates")
        ) {
          if (formData[key] === true) {
            tagsArr.push(key);
          }
        }
      });
      tagsArr.push(FORM_EMPLOYMENT_OPPORTUNITY);
      tagsArr.push(NTO);
      break;
    case FORM_TYPES.JFK_CONTACT_US:
      tagsArr.push(JFK_FORM_CONTACT_US);
      tagsArr.push(FORM_CONTACT_US);
      tagsArr.push(SMS_CONTACT_US);
      tagsArr.push(NTO);
      Object.keys(formData).forEach((key) => {
        if (key.includes("inquiryType")) {
          tagsArr.push(formData[key]);
        } else if (key.includes("emailUpdates") && formData[key] === true) {
          tagsArr.push(key);
        }
      });
      break;
  }

  // const mergeFields = {
  //   FNAME: formData.firstName,
  //   LNAME: formData.lastName,
  //   PHONE: formData.mobile,
  //   COMPANY: formData.company,
  //   MESSAGE: formData.message,
  //   WEBSITE: formData.website,
  //   KNOWMORE: formData.otherInformation,
  // };

  // const mergeFields = {
  //   FNAME: formData.firstName,
  //   LNAME: formData.lastName,
  //   ORG: company,
  //   AIRPORTEXP: airportExperience,
  //   MMERGE14: formData.mobile,
  //   MMERGE15: formData.website,
  //   BUS_YEARS: formData.yearsInBusiness,
  //   BUS_ABT: formData.aboutBusiness,
  // };

  const mergeFields = {
    FNAME: formData.firstName,
    LNAME: formData.lastName,
    COMPANY: company,
    AIRPORTEXP: airportExperience,
    PHONE: formData.mobile,
    WEBSITE: formData.website,
    BUS_YEARS: formData.yearsInBusiness,
    BUS_ABT: formData.aboutBusiness,
  };

  if (homeAddress1 || homeAddress2) {
    mergeFields["ADDRESS"] = {
      addr1: homeAddress1,
      addr2: homeAddress2,
      city: formData.city,
      state: formData.state,
      zip: `${formData.zipCode}`,
    };
  }

  if (businessAddress1 || businessAddress2) {
    mergeFields["B_ADDRESS"] = {
      addr1: businessAddress1,
      addr2: businessAddress2,
      city: formData.city,
      state: formData.state,
      zip: `${formData.zipCode}`,
    };
  }

  const subscriber = {
    email_address: formData.email,
    status_if_new: "subscribed",
    merge_fields: mergeFields,
  };

  const subscriberHash = crypto
    .createHash("md5")
    .update(subscriber.email_address.toLowerCase())
    .digest("hex");

  if (tagsArr.length > 0) {
    subscriber["tags"] = tagsArr;
  }

  try {
    console.log(JSON.stringify(subscriber));
    await mailchimp.lists.setListMember(
      process.env.MAILCHIMP_AUDIENCE_ID,
      subscriberHash,
      subscriber
    );
  } catch (error) {
    console.log("mailchimp error");
    console.log(JSON.stringify(error));
  }
};

module.exports = { addMember };
